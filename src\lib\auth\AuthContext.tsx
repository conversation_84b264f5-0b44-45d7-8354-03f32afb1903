'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase, db, Profile } from '../supabase';

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType);

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    let mounted = true;
    let retryCount = 0;
    const maxRetries = 3;

    const initializeAuth = async () => {
      try {
        console.log('Initializing auth...');
        
        // Get initial session with longer timeout to prevent hanging
        const { data: { session }, error } = await Promise.race([
          supabase.auth.getSession(),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Session timeout')), 15000)
          )
        ]);

        if (error) {
          console.error('Error getting session:', error);
          
          // Retry logic for session retrieval
          if (retryCount < maxRetries && error.message.includes('timeout')) {
            retryCount++;
            console.log(`Session timeout, retrying... (${retryCount}/${maxRetries})`);
            setTimeout(() => initializeAuth(), 2000 * retryCount);
            return;
          }
          
          if (mounted) {
            setUser(null);
            setProfile(null);
            setIsAdmin(false);
            setLoading(false);
          }
          return;
        }

        console.log('Session retrieved:', !!session?.user);

        if (mounted) {
          setUser(session?.user ?? null);

          if (session?.user) {
            await loadUserProfile(session.user.id);
          } else {
            setProfile(null);
            setIsAdmin(false);
            setLoading(false);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        
        // Session recovery attempt
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`Auth initialization failed, retrying... (${retryCount}/${maxRetries})`);
          setTimeout(() => initializeAuth(), 3000 * retryCount);
          return;
        }
        
        if (mounted) {
          setUser(null);
          setProfile(null);
          setIsAdmin(false);
          setLoading(false);
        }
      }
    };

    // Initialize auth state
    initializeAuth();

    // Listen for auth changes with better error handling
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        if (!mounted) return;

        // Handle specific auth events
        if (event === 'TOKEN_REFRESHED') {
          console.log('Token refreshed successfully');
        }

        if (event === 'SIGNED_IN') {
          console.log('User signed in');
        }

        // Handle logout case when session is null
        if (!session) {
          console.log('No session - user logged out');
          setUser(null);
          setProfile(null);
          setIsAdmin(false);
          setLoading(false);
          return;
        }

        // Don't set loading to true for logout cases to prevent UI flicker
        if (session) {
          setLoading(true);
        }

        setUser(session?.user ?? null);

        if (session?.user) {
          try {
            await loadUserProfile(session.user.id);
          } catch (profileError) {
            console.error('Error loading profile after auth change:', profileError);
            // Don't sign out user if profile loading fails
            setProfile(null);
            setIsAdmin(false);
            setLoading(false);
          }
        } else {
          setProfile(null);
          setIsAdmin(false);
          setLoading(false);
        }
      }
    );

    // Auto-refresh session every 5 minutes to prevent unexpected logouts
    const refreshInterval = setInterval(async () => {
      if (mounted) {
        try {
          const { data: { session } } = await supabase.auth.getSession();
          if (session) {
            console.log('Session auto-refresh check: active');
          } else {
            console.log('Session auto-refresh check: no active session');
          }
        } catch (error) {
          console.error('Session refresh check error:', error);
        }
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      mounted = false;
      subscription.unsubscribe();
      clearInterval(refreshInterval);
    };
  }, []);

  const loadUserProfile = async (userId: string) => {
    try {
      console.log('Loading profile for user:', userId);
      
      let userProfile = await db.getProfile(userId);

      if (!userProfile) {
        // Profile doesn't exist, try to create one
        console.log('Profile not found, attempting to create one for user:', userId);

        // Get user email from Supabase auth
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.email) {
          userProfile = await db.createProfile(userId, user.email, user.user_metadata?.display_name);
          if (userProfile) {
            console.log('Profile created successfully for user:', userId);
          } else {
            console.error('Failed to create profile for user:', userId);
          }
        }
      }

      if (userProfile) {
        console.log('Profile loaded successfully:', userProfile.display_name);
        setProfile(userProfile);
        // Check if user is admin by role or email
        const isUserAdmin = userProfile.role === 'admin' || userProfile.email === '<EMAIL>';
        setIsAdmin(isUserAdmin);
        console.log('User admin status:', isUserAdmin);
      } else {
        console.log('No profile available for user:', userId);
        setProfile(null);
        setIsAdmin(false);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      setProfile(null);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Supabase sign in error:', error);

        // Provide user-friendly error messages
        if (error.message.includes('Invalid login credentials')) {
          throw new Error('Invalid email or password. Please check your credentials and try again.');
        } else if (error.message.includes('Email not confirmed')) {
          throw new Error('Please confirm your email address before signing in.');
        } else if (error.message.includes('Too many requests')) {
          throw new Error('Too many login attempts. Please wait a few minutes and try again.');
        } else {
          throw new Error(error.message || 'Failed to sign in. Please try again.');
        }
      }
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      if (password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}/confirm-email`
        }
      });

      if (error) {
        console.error('Supabase sign up error:', error);

        // Provide user-friendly error messages
        if (error.message.includes('User already registered')) {
          throw new Error('An account with this email already exists. Please sign in instead.');
        } else if (error.message.includes('Password should be at least')) {
          throw new Error('Password must be at least 6 characters long');
        } else if (error.message.includes('Invalid email')) {
          throw new Error('Please enter a valid email address');
        } else {
          throw new Error(error.message || 'Failed to create account. Please try again.');
        }
      }

      if (!data.user) {
        throw new Error('Failed to create user account. Please try again.');
      }

      // Check if there's a pending team application for this email
      try {
        const application = await db.getApplicationByEmail(email);
        if (application) {
          // Link the application to the new user account
          await db.markAccountCreated(application.id, data.user.id);
          console.log('Linked team application to new user account');
        }
      } catch (linkError) {
        console.error('Error linking application to account:', linkError);
        // Don't throw here as the account creation was successful
      }

      // Note: User will need to confirm email before they can sign in
    } catch (error: unknown) {
      console.error('SignUp error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      console.log('Starting logout process...');
      
      // Clear local state first to provide immediate UI feedback
      setUser(null);
      setProfile(null);
      setIsAdmin(false);
      
      // Sign out from Supabase with global scope
      const { error } = await supabase.auth.signOut({ scope: 'global' });

      if (error) {
        console.error('Supabase logout error:', error);
        // Don't throw here - still want to clear local storage
      }

      // Clear localStorage as a fallback
      if (typeof window !== 'undefined') {
        localStorage.removeItem('supabase.auth.token');
        // Clear any other auth-related storage
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('supabase.auth.')) {
            localStorage.removeItem(key);
          }
        });
      }

      console.log('Logout completed successfully');

    } catch (error) {
      console.error('Logout error:', error);

      // Force clear everything even if logout fails
      setUser(null);
      setProfile(null);
      setIsAdmin(false);
      setLoading(false);

      // Clear localStorage
      if (typeof window !== 'undefined') {
        localStorage.clear();
      }
    }
  };

  return (
    <AuthContext.Provider value={{ user, profile, loading, signIn, signUp, logout, isAdmin }}>
      {children}
    </AuthContext.Provider>
  );
};