'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import AdminPanel from '@/components/auth/AdminPanel';
import InquiriesView from '@/components/admin/InquiriesView';
import BlogManagement from '@/components/admin/BlogManagement';
import TeamManagement from '@/components/admin/TeamManagement';
import AdminChat from '@/components/admin/AdminChat';
import HielLinksManagement from '@/components/admin/HielLinksManagement';
import { motion, AnimatePresence } from 'framer-motion';

// Real-time analytics cache
const analyticsCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes for real-time feel

interface AdminStats {
  totalInquiries: number;
  respondedInquiries: number;
  pendingInquiries: number;
  totalUsers: number;
  totalProfiles: number;
  totalPosts: number;
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical';
  responseTime: number;
  uptime: number;
}

interface RecentActivity {
  id: string;
  type: 'inquiry' | 'user' | 'post' | 'system' | 'hiellink' | 'error';
  message: string;
  timestamp: Date;
  severity: 'info' | 'warning' | 'error' | 'success';
  user?: string;
}

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState<'inquiries' | 'users' | 'overview' | 'blog' | 'team' | 'chat' | 'hiellinks'>('overview');
  const [stats, setStats] = useState<AdminStats>({
    totalInquiries: 0,
    respondedInquiries: 0,
    pendingInquiries: 0,
    totalUsers: 0,
    totalProfiles: 0,
    totalPosts: 0,
    systemHealth: 'excellent',
    responseTime: 0,
    uptime: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [notifications, setNotifications] = useState<number>(0);

  const { user, isAdmin, loading } = useAuth();

  // Real-time data fetching
  const fetchAdminStats = useCallback(async () => {
    const cached = analyticsCache.get('admin-stats');
    const now = Date.now();

    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      setStats(cached.data);
      setIsLoadingStats(false);
      return;
    }

    try {
      // Simulated real-time data - replace with actual API calls
      const mockStats: AdminStats = {
        totalInquiries: Math.floor(Math.random() * 150) + 50,
        respondedInquiries: Math.floor(Math.random() * 100) + 30,
        pendingInquiries: Math.floor(Math.random() * 20) + 5,
        totalUsers: Math.floor(Math.random() * 500) + 100,
        totalProfiles: Math.floor(Math.random() * 50) + 20,
        totalPosts: Math.floor(Math.random() * 100) + 25,
        systemHealth: ['excellent', 'good', 'warning'][Math.floor(Math.random() * 3)] as any,
        responseTime: Math.floor(Math.random() * 200) + 50,
        uptime: 99.8 + (Math.random() * 0.2),
      };

      setStats(mockStats);
      analyticsCache.set('admin-stats', { data: mockStats, timestamp: now });
      setLastRefresh(new Date());
      setIsLoadingStats(false);
    } catch (error) {
      console.error('Failed to fetch admin stats:', error);
      setIsLoadingStats(false);
    }
  }, []);

  // Fetch recent activity
  const fetchRecentActivity = useCallback(async () => {
    try {
      // Simulated recent activity - replace with actual API calls
      const activities: RecentActivity[] = [
        {
          id: '1',
          type: 'inquiry',
          message: 'New contact <NAME_EMAIL>',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          severity: 'info',
          user: '<EMAIL>'
        },
        {
          id: '2',
          type: 'user',
          message: 'New user registration completed',
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          severity: 'success',
          user: '<EMAIL>'
        },
        {
          id: '3',
          type: 'hiellink',
          message: 'HielLink profile published: @techstartup',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          severity: 'success',
          user: '<EMAIL>'
        },
        {
          id: '4',
          type: 'system',
          message: 'Database backup completed successfully',
          timestamp: new Date(Date.now() - 60 * 60 * 1000),
          severity: 'success',
        },
        {
          id: '5',
          type: 'post',
          message: 'Blog post "Future of Web Development" published',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          severity: 'info',
        },
      ];

      setRecentActivity(activities);
      
      // Calculate notifications (pending inquiries + system warnings)
      const pendingCount = stats.pendingInquiries;
      const systemWarnings = stats.systemHealth === 'warning' ? 1 : stats.systemHealth === 'critical' ? 2 : 0;
      setNotifications(pendingCount + systemWarnings);
    } catch (error) {
      console.error('Failed to fetch recent activity:', error);
    }
  }, [stats.pendingInquiries, stats.systemHealth]);

  // Auto-refresh functionality
  useEffect(() => {
    fetchAdminStats();
    fetchRecentActivity();

    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchAdminStats();
        fetchRecentActivity();
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(interval);
    }
  }, [fetchAdminStats, fetchRecentActivity, autoRefresh]);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="inline-flex items-center space-x-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="text-lg font-medium text-gray-900 dark:text-white">Loading admin dashboard...</span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            Fetching real-time data and system status
          </p>
        </motion.div>
      </div>
    );
  }

  // Access denied
  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700"
        >
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">
            Access Denied
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You don&apos;t have permission to access the admin dashboard.
          </p>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => window.history.back()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            Go Back
          </motion.button>
        </motion.div>
      </div>
    );
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'good': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';
      case 'warning': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'excellent': return '💚';
      case 'good': return '💙';
      case 'warning': return '⚠️';
      case 'critical': return '🔴';
      default: return '⚪';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'inquiry': return '📧';
      case 'user': return '👤';
      case 'post': return '📝';
      case 'system': return '⚙️';
      case 'hiellink': return '🔗';
      case 'error': return '❌';
      default: return 'ℹ️';
    }
  };

  const getActivityColor = (severity: string) => {
    switch (severity) {
      case 'success': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-blue-500';
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊', count: 0 },
    { id: 'inquiries', label: 'Inquiries', icon: '📧', count: stats.pendingInquiries },
    { id: 'blog', label: 'Blog', icon: '📝', count: 0 },
    { id: 'team', label: 'Team', icon: '🚀', count: 0 },
    { id: 'hiellinks', label: 'HielLinks', icon: '🔗', count: 0 },
    { id: 'chat', label: 'Chat', icon: '💬', count: 0 },
    { id: 'users', label: 'Users', icon: '👥', count: 0 },
  ] as const;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Enhanced Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Admin Dashboard
              </h1>
              <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                <span>Welcome back, {user?.email}</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${getActivityColor('success')}`}></div>
                  <span>System {stats.systemHealth}</span>
                </div>
                <span>Last updated: {lastRefresh.toLocaleTimeString()}</span>
              </div>
            </div>
            
            {/* Quick Actions */}
            <div className="flex items-center space-x-3 mt-4 sm:mt-0">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  autoRefresh 
                    ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' 
                    : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                }`}
              >
                {autoRefresh ? '🔄 Auto-refresh ON' : '⏸️ Auto-refresh OFF'}
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  fetchAdminStats();
                  fetchRecentActivity();
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                🔄 Refresh Now
              </motion.button>

              {/* Notifications */}
              {notifications > 0 && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="relative"
                >
                  <div className="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                    {notifications > 99 ? '99+' : notifications}
                  </div>
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
                </motion.div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Enhanced Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-8"
        >
          <nav className="flex space-x-1 p-1">
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`relative flex-1 flex items-center justify-center py-3 px-4 text-sm font-medium rounded-md transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
                {tab.count > 0 && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                      activeTab === tab.id
                        ? 'bg-white/20 text-white'
                        : 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
                    }`}
                  >
                    {tab.count}
                  </motion.span>
                )}
              </motion.button>
            ))}
          </nav>
        </motion.div>

        {/* Enhanced Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'overview' && (
              <OverviewTab 
                stats={stats} 
                recentActivity={recentActivity} 
                isLoading={isLoadingStats}
                onTabChange={setActiveTab}
                getHealthColor={getHealthColor}
                getHealthIcon={getHealthIcon}
                getActivityIcon={getActivityIcon}
                getActivityColor={getActivityColor}
              />
            )}
            {activeTab === 'inquiries' && <InquiriesView />}
            {activeTab === 'blog' && <BlogManagement />}
            {activeTab === 'team' && <TeamManagement />}
            {activeTab === 'hiellinks' && <HielLinksManagement />}
            {activeTab === 'chat' && <AdminChat />}
            {activeTab === 'users' && <AdminPanel />}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}

interface OverviewTabProps {
  stats: AdminStats;
  recentActivity: RecentActivity[];
  isLoading: boolean;
  onTabChange: (tab: any) => void;
  getHealthColor: (health: string) => string;
  getHealthIcon: (health: string) => string;
  getActivityIcon: (type: string) => string;
  getActivityColor: (severity: string) => string;
}

function OverviewTab({ 
  stats, 
  recentActivity, 
  isLoading, 
  onTabChange,
  getHealthColor,
  getHealthIcon,
  getActivityIcon,
  getActivityColor 
}: OverviewTabProps) {
  const formatUptime = (uptime: number) => {
    return `${uptime.toFixed(2)}%`;
  };

  const formatResponseTime = (time: number) => {
    return `${time}ms`;
  };

  const statCards = [
    {
      title: 'Total Inquiries',
      value: stats.totalInquiries,
      icon: '📧',
      color: 'blue',
      change: '+12%',
      trend: 'up',
      action: () => onTabChange('inquiries')
    },
    {
      title: 'Responded',
      value: stats.respondedInquiries,
      icon: '✅',
      color: 'green',
      change: '+8%',
      trend: 'up',
      action: () => onTabChange('inquiries')
    },
    {
      title: 'Pending',
      value: stats.pendingInquiries,
      icon: '⏳',
      color: 'yellow',
      change: '-5%',
      trend: 'down',
      action: () => onTabChange('inquiries')
    },
    {
      title: 'Total Users',
      value: stats.totalUsers,
      icon: '👥',
      color: 'purple',
      change: '+15%',
      trend: 'up',
      action: () => onTabChange('users')
    },
    {
      title: 'HielLinks Profiles',
      value: stats.totalProfiles,
      icon: '🔗',
      color: 'indigo',
      change: '+22%',
      trend: 'up',
      action: () => onTabChange('hiellinks')
    },
    {
      title: 'Blog Posts',
      value: stats.totalPosts,
      icon: '📝',
      color: 'pink',
      change: '+7%',
      trend: 'up',
      action: () => onTabChange('blog')
    },
  ];

  return (
    <div className="space-y-8">
      {/* Enhanced Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ y: -4, transition: { duration: 0.2 } }}
            onClick={card.action}
            className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-lg transition-all duration-200"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-lg bg-${card.color}-100 dark:bg-${card.color}-900/30`}>
                <span className="text-2xl">{card.icon}</span>
              </div>
              <div className={`flex items-center space-x-1 text-sm ${
                card.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                <span>{card.trend === 'up' ? '↗️' : '↘️'}</span>
                <span>{card.change}</span>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                {card.title}
              </p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white">
                {isLoading ? (
                  <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-8 w-16 rounded"></div>
                ) : (
                  card.value.toLocaleString()
                )}
              </p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* System Health & Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <span className="mr-2">🏥</span>
            System Health
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Overall Status</span>
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${getHealthColor(stats.systemHealth)}`}>
                <span>{getHealthIcon(stats.systemHealth)}</span>
                <span className="font-medium capitalize">{stats.systemHealth}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Uptime</span>
              <span className="font-bold text-gray-900 dark:text-white">
                {formatUptime(stats.uptime)}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Response Time</span>
              <span className="font-bold text-gray-900 dark:text-white">
                {formatResponseTime(stats.responseTime)}
              </span>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <span className="mr-2">⚡</span>
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 gap-3">
            {[
              { label: 'View New Inquiries', icon: '📧', action: () => onTabChange('inquiries'), count: stats.pendingInquiries },
              { label: 'Manage Users', icon: '👥', action: () => onTabChange('users'), count: 0 },
              { label: 'Create Blog Post', icon: '📝', action: () => onTabChange('blog'), count: 0 },
              { label: 'Check HielLinks', icon: '🔗', action: () => onTabChange('hiellinks'), count: 0 },
            ].map((action, index) => (
              <motion.button
                key={action.label}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={action.action}
                className="flex items-center justify-between p-3 text-left border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
              >
                <div className="flex items-center">
                  <span className="text-xl mr-3">{action.icon}</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {action.label}
                  </span>
                </div>
                {action.count > 0 && (
                  <span className="bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400 px-2 py-1 rounded-full text-xs font-medium">
                    {action.count}
                  </span>
                )}
              </motion.button>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Enhanced Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <span className="mr-2">📊</span>
            Recent Activity
          </h3>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Live updates every 30 seconds
          </span>
        </div>
        
        <div className="space-y-4">
          <AnimatePresence>
            {recentActivity.map((activity, index) => (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ delay: index * 0.05 }}
                className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <div className={`w-2 h-2 rounded-full mt-2 ${getActivityColor(activity.severity)}`}></div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-lg">{getActivityIcon(activity.type)}</span>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {activity.message}
                    </p>
                  </div>
                  <div className="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400">
                    <span>{activity.timestamp.toLocaleTimeString()}</span>
                    {activity.user && (
                      <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                        {activity.user}
                      </span>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {recentActivity.length === 0 && (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <span className="text-4xl mb-2 block">📊</span>
              No recent activity to display
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}