'use client';

import { HielProfile } from '@/lib/supabase';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect, useMemo, useCallback } from 'react';

interface HielProfileListProps {
  profiles: HielProfile[];
  canCreateProfile: boolean;
  onCreateProfile: () => void;
  onEditProfile: (profile: HielProfile) => void;
  onDeleteProfile: (profileId: string) => void;
  isLoading?: boolean;
  showAnalytics?: boolean;
}

// Cache for profile analytics
const analyticsCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export default function HielProfileList({
  profiles,
  canCreateProfile,
  onCreateProfile,
  onEditProfile,
  onDeleteProfile,
  isLoading = false,
  showAnalytics = true,
}: HielProfileListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'views' | 'created'>('created');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedProfile, setSelectedProfile] = useState<string | null>(null);
  const [profileStats, setProfileStats] = useState<Map<string, any>>(new Map());
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());
  const [hoveredProfile, setHoveredProfile] = useState<string | null>(null);

  // Optimized filtering and sorting with useMemo
  const filteredAndSortedProfiles = useMemo(() => {
    let filtered = profiles;

    // Apply search filter
    if (searchTerm) {
      filtered = profiles.filter(profile =>
        profile.business_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        profile.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        profile.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.business_name.toLowerCase();
          bValue = b.business_name.toLowerCase();
          break;
        case 'views':
          aValue = a.view_count || 0;
          bValue = b.view_count || 0;
          break;
        case 'created':
        default:
          aValue = new Date(a.created_at).getTime();
          bValue = new Date(b.created_at).getTime();
          break;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [profiles, searchTerm, sortBy, sortOrder]);

  // Cached analytics fetching
  const fetchProfileAnalytics = useCallback(async (profileId: string) => {
    const cached = analyticsCache.get(profileId);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      return cached.data;
    }

    try {
      // Simulated analytics fetch - replace with actual API call
      const mockAnalytics = {
        viewsToday: Math.floor(Math.random() * 50),
        clicksToday: Math.floor(Math.random() * 25),
        topReferrer: 'Direct',
        conversionRate: Math.floor(Math.random() * 100) / 10,
      };

      analyticsCache.set(profileId, { data: mockAnalytics, timestamp: now });
      return mockAnalytics;
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      return null;
    }
  }, []);

  // Load analytics for visible profiles
  useEffect(() => {
    if (showAnalytics && profiles.length > 0) {
      profiles.forEach(async (profile) => {
        const analytics = await fetchProfileAnalytics(profile.id);
        if (analytics) {
          setProfileStats(prev => new Map(prev.set(profile.id, analytics)));
        }
      });
    }
  }, [profiles, showAnalytics, fetchProfileAnalytics]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'suspended':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleImageError = (profileId: string) => {
    setImageErrors(prev => new Set(prev.add(profileId)));
  };

  const getPerformanceInsight = (profile: HielProfile) => {
    const stats = profileStats.get(profile.id);
    if (!stats) return null;

    const ctr = profile.view_count > 0 ? (profile.click_count / profile.view_count) * 100 : 0;
    
    if (ctr > 10) return { message: 'High performance', color: 'text-green-600', icon: '📈' };
    if (ctr > 5) return { message: 'Good performance', color: 'text-blue-600', icon: '📊' };
    if (ctr > 2) return { message: 'Average performance', color: 'text-yellow-600', icon: '📋' };
    return { message: 'Needs improvement', color: 'text-red-600', icon: '📉' };
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex justify-between items-center">
          <div className="h-7 bg-gray-200 dark:bg-gray-700 rounded-md w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg w-36 animate-pulse"></div>
        </div>

        {/* Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="h-16 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
              <div className="p-4 space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse"></div>
                  </div>
                </div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="flex justify-between">
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse"></div>
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse"></div>
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Enhanced Header with Search and Sort */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Your HielLinks Profiles ({profiles.length})
          </h2>
          {canCreateProfile && (
            <motion.button
              onClick={onCreateProfile}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <span className="flex items-center space-x-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Create New Profile</span>
              </span>
            </motion.button>
          )}
        </div>

        {/* Search and Filter Controls */}
        {profiles.length > 0 && (
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="Search profiles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              />
              <svg className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>

            {/* Sort Controls */}
            <div className="flex space-x-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="created">Created Date</option>
                <option value="name">Name</option>
                <option value="views">Views</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Profiles Grid */}
      <AnimatePresence mode="wait">
        {filteredAndSortedProfiles.length === 0 ? (
          <motion.div
            key="empty"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">
              {searchTerm ? '🔍' : '🔗'}
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {searchTerm ? 'No matching profiles found' : 'No profiles yet'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {searchTerm 
                ? 'Try adjusting your search terms or create a new profile'
                : 'Create your first HielLinks profile to get started'
              }
            </p>
            {canCreateProfile && !searchTerm && (
              <motion.button
                onClick={onCreateProfile}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md"
              >
                Create Your First Profile
              </motion.button>
            )}
          </motion.div>
        ) : (
          <motion.div
            key="grid"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {filteredAndSortedProfiles.map((profile, index) => {
              const stats = profileStats.get(profile.id);
              const insight = getPerformanceInsight(profile);
              
              return (
                <motion.div
                  key={profile.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05 }}
                  whileHover={{ y: -4, transition: { duration: 0.2 } }}
                  onHoverStart={() => setHoveredProfile(profile.id)}
                  onHoverEnd={() => setHoveredProfile(null)}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-all duration-200"
                >
                  {/* Profile Header with Enhanced Visuals */}
                  <div 
                    className="h-16 relative group"
                    style={{ 
                      backgroundColor: profile.theme_color,
                      background: profile.background_image_url && !imageErrors.has(profile.id) 
                        ? `linear-gradient(45deg, ${profile.theme_color}80, ${profile.theme_color}40), url(${profile.background_image_url})`
                        : profile.theme_color
                    }}
                  >
                    {profile.background_image_url && !imageErrors.has(profile.id) && (
                      <Image
                        src={profile.background_image_url}
                        alt=""
                        fill
                        className="object-cover opacity-30 group-hover:opacity-40 transition-opacity duration-200"
                        onError={() => handleImageError(profile.id)}
                      />
                    )}
                    <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent"></div>

                    {/* Status Badge with Animation */}
                    <motion.div 
                      className="absolute top-3 right-3"
                      whileHover={{ scale: 1.05 }}
                    >
                      <span className={`px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm ${getStatusColor(profile.status)}`}>
                        {profile.status === 'published' && '🌐 '}
                        {profile.status === 'draft' && '📝 '}
                        {profile.status === 'suspended' && '⚠️ '}
                        {profile.status.charAt(0).toUpperCase() + profile.status.slice(1)}
                      </span>
                    </motion.div>

                    {/* Performance Indicator */}
                    {insight && hoveredProfile === profile.id && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="absolute top-3 left-3 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium"
                      >
                        <span className={insight.color}>
                          {insight.icon} {insight.message}
                        </span>
                      </motion.div>
                    )}
                  </div>

                  {/* Enhanced Profile Content */}
                  <div className="p-4 relative">
                    {/* Logo with Better Positioning */}
                    <div className="flex items-start space-x-3 mb-3">
                      <motion.div 
                        className="w-8 h-8 rounded-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 flex items-center justify-center overflow-hidden shadow-sm flex-shrink-0"
                        whileHover={{ scale: 1.1 }}
                      >
                        {profile.logo_url && !imageErrors.has(`logo-${profile.id}`) ? (
                          <Image
                            src={profile.logo_url}
                            alt={profile.business_name}
                            width={24}
                            height={24}
                            className="object-cover rounded-full"
                            onError={() => handleImageError(`logo-${profile.id}`)}
                          />
                        ) : (
                          <span className="text-xs font-bold text-gray-600 dark:text-gray-400">
                            {profile.business_name.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </motion.div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-1 truncate">
                          {profile.business_name}
                        </h3>
                        <p className="text-sm text-blue-600 dark:text-blue-400 mb-2 truncate font-medium">
                          @{profile.username}
                        </p>
                      </div>
                    </div>

                    {profile.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2 leading-relaxed">
                        {profile.description}
                      </p>
                    )}

                    {/* Enhanced Stats with Real-time Data */}
                    <div className="grid grid-cols-3 gap-3 mb-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-900 dark:text-white">
                          {profile.view_count || 0}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Views</div>
                        {stats && (
                          <div className="text-xs text-green-600 dark:text-green-400">
                            +{stats.viewsToday} today
                          </div>
                        )}
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-900 dark:text-white">
                          {profile.click_count || 0}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Clicks</div>
                        {stats && (
                          <div className="text-xs text-blue-600 dark:text-blue-400">
                            +{stats.clicksToday} today
                          </div>
                        )}
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-900 dark:text-white">
                          {profile.links?.length || 0}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">Links</div>
                        {stats && (
                          <div className="text-xs text-purple-600 dark:text-purple-400">
                            {stats.conversionRate}% CTR
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Enhanced Actions */}
                    <div className="flex space-x-2 mb-3">
                      <motion.button
                        onClick={() => onEditProfile(profile)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-1"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        <span>Edit</span>
                      </motion.button>
                      
                      {profile.status === 'published' && (
                        <Link
                          href={`/hielLinks/${profile.username}`}
                          target="_blank"
                          className="flex-1"
                        >
                          <motion.div
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className="w-full bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-1"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            <span>View</span>
                          </motion.div>
                        </Link>
                      )}
                      
                      <motion.button
                        onClick={() => onDeleteProfile(profile.id)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center justify-center"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </motion.button>
                    </div>

                    {/* Creation Date with Last Activity */}
                    <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                        <span>Created {formatDate(profile.created_at)}</span>
                        {profile.updated_at !== profile.created_at && (
                          <span>Updated {formatDate(profile.updated_at)}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Enhanced Limit Warning */}
      {!canCreateProfile && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg shadow-sm"
        >
          <div className="flex items-start">
            <div className="text-yellow-600 dark:text-yellow-400 mr-3 text-xl">⚠️</div>
            <div className="flex-1">
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                Profile Limit Reached
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-3">
                You&apos;ve reached your maximum number of profiles. Upgrade your plan to create more profiles and unlock advanced features.
              </p>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-all duration-200"
              >
                Upgrade Plan
              </motion.button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
