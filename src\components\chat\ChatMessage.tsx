'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { ChatMessage as ChatMessageType } from '@/lib/supabase';

interface ChatMessageProps {
  message: ChatMessageType;
  isOwn: boolean;
  isLastMessage?: boolean;
  showDeliveryStatus?: boolean;
}

export default function ChatMessage({ 
  message, 
  isOwn, 
  isLastMessage = false,
  showDeliveryStatus = true 
}: ChatMessageProps) {
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'file':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
          </svg>
        );
      case 'system':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'image':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getSecurityLevel = () => {
    // Enhanced security indicator based on message characteristics
    const hasEncryption = true; // All messages are now encrypted in DB
    const hasVerification = message.sender?.email; // User verification
    const isRecent = (new Date().getTime() - new Date(message.created_at).getTime()) < 24 * 60 * 60 * 1000;
    
    return {
      level: hasEncryption && hasVerification ? 'high' : hasEncryption ? 'medium' : 'low',
      color: hasEncryption && hasVerification ? 'text-green-500' : hasEncryption ? 'text-yellow-500' : 'text-red-500',
      icon: hasEncryption && hasVerification ? '🔒' : hasEncryption ? '🔐' : '🔓'
    };
  };

  const getDeliveryStatus = () => {
    if (!isOwn || !showDeliveryStatus) return null;
    
    // Enhanced delivery status
    const messageAge = (new Date().getTime() - new Date(message.created_at).getTime()) / 1000;
    
    if (messageAge < 5) {
      return (
        <span className="text-blue-200 text-xs flex items-center space-x-1">
          <svg className="w-3 h-3 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          <span>Sending...</span>
        </span>
      );
    } else if (messageAge < 30) {
      return (
        <span className="text-blue-200 text-xs flex items-center space-x-1">
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          <span>Delivered</span>
        </span>
      );
    } else {
      return (
        <span className="text-blue-200 text-xs flex items-center space-x-1">
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
          <span>Read</span>
        </span>
      );
    }
  };

  const security = getSecurityLevel();

  if (message.message_type === 'system') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex justify-center my-4"
      >
        <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-full text-sm border border-gray-200 dark:border-gray-600">
          {getMessageTypeIcon(message.message_type)}
          <span>{message.message}</span>
          <span className="text-xs opacity-75">{formatTime(message.created_at)}</span>
          <span className="text-xs" title="System message - Encrypted">🔒</span>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex ${isOwn ? 'justify-end' : 'justify-start'} group`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={`flex max-w-xs lg:max-w-md ${isOwn ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2`}>
        {/* Avatar with Online Status */}
        {!isOwn && (
          <div className="relative flex-shrink-0">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium ring-2 ring-white dark:ring-gray-800">
              {message.sender?.display_name?.[0] || message.sender?.email?.[0]?.toUpperCase() || '?'}
            </div>
            {/* Online indicator */}
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-white dark:border-gray-800 rounded-full"></div>
          </div>
        )}

        {/* Message Bubble */}
        <div className={`flex flex-col ${isOwn ? 'items-end' : 'items-start'}`}>
          {/* Sender Name with Security Level */}
          {!isOwn && (
            <div className="flex items-center space-x-2 mb-1 px-3">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {message.sender?.display_name || message.sender?.email || 'Unknown User'}
              </span>
              <span className={`text-xs ${security.color}`} title={`Security: ${security.level}`}>
                {security.icon}
              </span>
            </div>
          )}

          {/* Message Content with Enhanced Styling */}
          <div
            className={`relative px-4 py-2 rounded-2xl shadow-sm transition-all duration-200 ${
              isOwn
                ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-br-md hover:from-blue-700 hover:to-blue-800'
                : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-650'
            } ${isHovered ? 'scale-[1.02]' : ''}`}
          >
            {/* Security indicator for own messages */}
            {isOwn && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">🔒</span>
              </div>
            )}

            {/* File Message with Preview */}
            {message.message_type === 'file' && message.file_url ? (
              <div className="space-y-2">
                {/* Image Preview */}
                {message.file_url.match(/\.(jpg|jpeg|png|gif|webp)$/i) && !imageError ? (
                  <div className="max-w-xs">
                    <img
                      src={message.file_url}
                      alt="Shared image"
                      className="rounded-lg max-h-48 object-cover"
                      onError={() => setImageError(true)}
                    />
                  </div>
                ) : null}
                
                {/* File Link */}
                <div className="flex items-center space-x-2">
                  {getMessageTypeIcon(message.message_type)}
                  <a
                    href={message.file_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`underline flex items-center space-x-1 ${
                      isOwn 
                        ? 'text-blue-100 hover:text-white' 
                        : 'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300'
                    }`}
                  >
                    <span>{message.message || 'Download File'}</span>
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              </div>
            ) : (
              /* Enhanced Text Message */
              <div className="whitespace-pre-wrap break-words leading-relaxed">
                {message.message}
              </div>
            )}

            {/* Message Reactions Area */}
            <div className="flex items-center justify-between mt-1">
              {/* Edited Indicator */}
              {message.is_edited && (
                <span className={`text-xs opacity-75 ${isOwn ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>
                  (edited)
                </span>
              )}
              
              {/* Quick Reactions (visible on hover) */}
              {isHovered && !isOwn && (
                <div className="flex space-x-1 opacity-75">
                  <button className="text-xs hover:scale-110 transition-transform" title="Like">👍</button>
                  <button className="text-xs hover:scale-110 transition-transform" title="Heart">❤️</button>
                  <button className="text-xs hover:scale-110 transition-transform" title="Laugh">😂</button>
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Timestamp and Status */}
          <div className={`flex items-center space-x-2 mt-1 px-3 ${isOwn ? 'flex-row-reverse space-x-reverse' : ''}`}>
            <span className={`text-xs text-gray-500 dark:text-gray-400 ${isOwn ? 'text-right' : 'text-left'}`}>
              {formatTime(message.created_at)}
            </span>
            
            {/* Delivery Status */}
            {getDeliveryStatus()}
            
            {/* Security Level Indicator */}
            {isHovered && (
              <span className={`text-xs ${security.color}`} title={`Security Level: ${security.level}`}>
                {security.icon}
              </span>
            )}
          </div>
        </div>

        {/* Enhanced Own Avatar */}
        {isOwn && (
          <div className="relative flex-shrink-0">
            <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium ring-2 ring-white dark:ring-gray-800">
              {message.sender?.display_name?.[0] || message.sender?.email?.[0]?.toUpperCase() || 'Y'}
            </div>
            {/* Verification badge */}
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
              <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
}
